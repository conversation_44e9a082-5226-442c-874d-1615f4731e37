<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>WDA Monitor - Visualizations</title>
	<!-- Include all the same CSS and JS dependencies as index.html -->
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
	<link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
	<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
	<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.0/papaparse.min.js"></script>
	<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
	<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
	<!-- Include all the same styles from index.html -->
	<style>
		.alert {
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 1000;
			min-width: 300px;
			box-shadow: 0 4px 6px rgba(0,0,0,0.1);
		}

		.alert-danger {
			color: #721c24;
			background-color: #f8d7da;
			border-color: #f5c6cb;
		}

		/* Stat card styles */
		.stat-card {
			border-radius: 10px;
			transition: all 0.3s ease;
			border: none;
			box-shadow: 0 4px 6px rgba(0,0,0,0.1);
			margin-bottom: 1.5rem;
		}
		.stat-card:hover {
			transform: translateY(-5px);
			box-shadow: 0 6px 12px rgba(0,0,0,0.15);
		}
		.stat-icon {
			font-size: 2.5rem;
			opacity: 0.8;
		}
		.stat-card.total-parts { background: linear-gradient(45deg, #4158D0, #C850C0); color: white; }
		.stat-card.expired-parts { background: linear-gradient(45deg, #FF416C, #FF4B2B); color: white; }
		.stat-card.active-parts { background: linear-gradient(45deg, #11998e, #38ef7d); color: white; }
		.stat-card.error-parts { background: linear-gradient(45deg, #755BEA, #FF72C0); color: white; }
		.stat-card.missing-parts { background: linear-gradient(45deg, #FF416C, #FF4B2B); color: white; }
		.stat-card.module-count { background: linear-gradient(45deg, #4354b3, #442bff); color: white; }
		.stat-card.issue-module-count { background: linear-gradient(45deg, #5d256a, #ff2bb1); color: white; }

		.alert-danger {
			background-color: #f8d7da;
			color: #721c24;
			border: 1px solid #f5c6cb;
			border-radius: 4px;
			padding: 15px;
			margin: 10px 0;
		}

		/* Update chart container styles */
		.chart-container {
			position: relative;
			min-height: 400px;
			background: white;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
			padding: 15px;
			margin-bottom: 20px;
			/*overflow: auto;*/
		}

		/* Update module chart styles */
		.module-chart-wrapper {
			width: 100%;
			overflow-x: hidden;
			overflow-y: hidden;
			padding-bottom: 15px;
			position: relative;  /* For loading indicator positioning */
		}

		#moduleChart {
			width: 100% !important;
			height: 500px !important;
			min-width: 500px;  /* Match the chart width */
		}

		/* Update loading indicator for module chart */
		.module-chart-wrapper .chart-loading {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(255, 255, 255, 0.9);
			display: none;
			justify-content: center;
			align-items: center;
			z-index: 100;
		}



		.chart-error {
			display: flex;
			align-items: center;
			justify-content: center;
			min-height: 200px;
			padding: 20px;
			text-align: center;
			color: #721c24;
			background-color: #f8d7da;
			border: 1px solid #f5c6cb;
			border-radius: 4px;
			margin: 10px;
		}

		.select2-container {
			width: 100% !important;
		}
		.select2-selection--multiple {
			max-height: 80px; /* or any height you prefer */
			overflow-y: auto !important;
			overflow-x: hidden;
		}

		.filter-count {
			display: none;
			margin-left: 5px;
			background-color: #e9ecef;
			padding: 2px 6px;
			border-radius: 10px;
			font-size: 0.8em;
		}

		.filter-section {
			background-color: #f8f9fa;
			padding: 20px;
			border-radius: 5px;
			margin-bottom: 20px;
		}

		.btn-filter {
			margin-top: 32px;
		}

		.chart-title {
			margin-bottom: 15px;
			color: #2c3e50;
		}

		.navbar {
			margin-bottom: 20px;
		}

		.loading {
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(255, 255, 255, 0.8);
			display: none;
			justify-content: center;
			align-items: center;
			z-index: 9999;
		}

		.loading-spinner {
			width: 50px;
			height: 50px;
			border: 5px solid #f3f3f3;
			border-top: 5px solid #3498db;
			border-radius: 50%;
			animation: spin 1s linear infinite;
			margin: auto;
		}

		.chart-loading {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: rgba(255, 255, 255, 0.9);
			display: none;
			justify-content: center;
			align-items: center;
			z-index: 100;
			border-radius: 8px;
		}

		.notification {
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 1000;
			min-width: 300px;
			padding: 15px;
			border-radius: 4px;
			box-shadow: 0 4px 6px rgba(0,0,0,0.1);
			display: none;
			animation: slideIn 0.3s ease-out;
		}

		@keyframes slideIn {
			from {
				transform: translateX(100%);
				opacity: 0;
			}
			to {
				transform: translateX(0);
				opacity: 1;
			}
		}

		.notification.success {
			background-color: #d4edda;
			color: #155724;
			border: 1px solid #c3e6cb;
		}

		.notification.error {
			background-color: #f8d7da;
			color: #721c24;
			border: 1px solid #f5c6cb;
		}

		.chart-container {
			position: relative;
			min-height: 400px;
			background: white;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
			padding: 15px;
			margin-bottom: 20px;
		}


		/* Data Table Styles */
		.data-table-container {
			margin-top: 2rem;
			overflow-x: auto;
			-webkit-overflow-scrolling: touch;
			max-height: 600px;  /* Set maximum height */
			overflow-y: auto;   /* Enable vertical scrolling */
		}

		.data-table {
			min-width: 1200px;  /* Ensure minimum width to prevent cramping */
			width: 100%;
			border-collapse: separate;
			border-spacing: 0;
			background: white;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
		}

		.data-table th {
			background-color: #f8f9fa;
			cursor: pointer;
			padding: 12px;
			text-align: left;
			border-bottom: 2px solid #dee2e6;
		}

		/* Make header sticky */
		.data-table thead th {
			position: sticky;
			top: 0;
			background-color: #f8f9fa;
			z-index: 1;
			box-shadow: 0 2px 2px -1px rgba(0,0,0,0.1);
		}

		.data-table th:hover {
			background-color: #e9ecef;
		}

		.data-table td {
			padding: 12px;
			border-bottom: 1px solid #dee2e6;
		}

		.data-table tbody tr:hover {
			background-color: #f8f9fa;
		}

		.sort-icon {
			margin-left: 5px;
		}

		.data-table-wrapper {
			margin: 20px;
			padding: 0;
			background: white;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
		}

		/* Data table styles */
		.data-table td {
			text-align: right;  /* Right align numeric columns */
			padding: 8px 12px;
		}

		.data-table td:first-child {
			text-align: left;   /* Left align module names */
		}

		.data-table th {
			text-align: right;  /* Right align header cells */
			padding: 12px;
			background-color: #f8f9fa;
			font-weight: 600;
		}

		.data-table th:first-child {
			text-align: left;   /* Left align module header */
		}

		.data-table tr:hover {
			background-color: rgba(0,0,0,0.02);
		}

		/* Add zebra striping */
		.data-table tbody tr:nth-child(even) {
			background-color: rgba(0,0,0,0.01);
		}

		/* Add subtle borders */
		.data-table td, .data-table th {
			border-bottom: 1px solid #dee2e6;
		}

		/* Column width styles */
		.data-table th,
		.data-table td {
			white-space: nowrap;  /* Prevent text wrapping */
		}

		/* Module column (wider for names) */
		.data-table th:first-child,
		.data-table td:first-child {
			min-width: 200px;
			max-width: 300px;
			white-space: normal;  /* Allow wrapping for module names */
			word-break: break-word;
		}

		/* Count columns (narrower for numbers) */
		.data-table th[data-sort*="count"],
		.data-table td:nth-child(2),
		.data-table td:nth-child(3),
		.data-table td:nth-child(5),
		.data-table td:nth-child(7),
		.data-table td:nth-child(9) {
			min-width: 100px;
		}

		/* Percentage columns (narrow for percentages) */
		.data-table th[data-sort*="percentage"],
		.data-table td:nth-child(4),
		.data-table td:nth-child(6),
		.data-table td:nth-child(8),
		.data-table td:nth-child(10) {
			min-width: 80px;
		}

		/* Add color coding for percentages */
		.data-table td:nth-child(4),
		.data-table td:nth-child(6),
		.data-table td:nth-child(8),
		.data-table td:nth-child(10) {
			position: relative;
		}

		.data-table td:nth-child(4)::after,
		.data-table td:nth-child(6)::after,
		.data-table td:nth-child(8)::after,
		.data-table td:nth-child(10)::after {
			content: '';
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			width: var(--percentage-width);
			background-color: rgba(0, 123, 255, 0.1);
			z-index: 0;
		}

		/* Ensure text stays above the percentage background */
		.data-table td {
			position: relative;
			z-index: 1;
		}

		/* Update legend styles for better compactness */
		.table-legend {
			display: flex;
			flex-wrap: wrap;
			gap: 15px;
			margin-bottom: 15px;
			padding: 8px 12px;
			background: #f8f9fa;
			border-radius: 4px;
			font-size: 0.85rem;
		}

		.legend-item {
			display: flex;
			align-items: center;
			gap: 6px;
			white-space: nowrap;
		}

		.legend-color {
			width: 16px;
			height: 16px;
			border-radius: 3px;
		}

		.legend-color.good { background-color: rgba(40, 167, 69, 0.2); }
		.legend-color.warning { background-color: rgba(255, 193, 7, 0.2); }
		.legend-color.critical { background-color: rgba(220, 53, 69, 0.2); }

		/* Add color coding classes for percentages */
		.percentage-low { color: #28a745; }    /* Green for good values */
		.percentage-medium { color: #ffc107; }  /* Yellow for warning */
		.percentage-high { color: #dc3545; }    /* Red for critical */

		/* Update the background colors for percentage bars */
		.percentage-low::after { background-color: rgba(40, 167, 69, 0.1); }
		.percentage-medium::after { background-color: rgba(255, 193, 7, 0.1); }
		.percentage-high::after { background-color: rgba(220, 53, 69, 0.1); }

		/* Update table header styles */
		.data-table th {
			text-align: right;
			padding: 12px 8px;
			font-size: 0.9rem;
			line-height: 1.2;
			vertical-align: bottom;
			position: sticky;
			top: 0;
			background-color: #f8f9fa;
			z-index: 1;
		}

		.data-table th:first-child {
			text-align: left;
		}

		.sort-icon {
			opacity: 0.5;
			margin-left: 4px;
			font-size: 0.8em;
		}

		/* Add hover effect for sort icons */
		.data-table th:hover .sort-icon {
			opacity: 1;
		}

		/* Add subtle border to table header */
		.data-table thead {
			border-bottom: 2px solid #dee2e6;
		}

		/* Improve percentage visualization */
		.data-table td[style*="--percentage-width"]::after {
			transition: width 0.3s ease-in-out;
			opacity: 0.15;
		}

		/* Make table more responsive */
		@media (max-width: 1400px) {
			.data-table th {
				font-size: 0.85rem;
				padding: 8px 6px;
			}

			.data-table td {
				font-size: 0.9rem;
				padding: 6px;
			}
		}

		/* Add hover effect for rows */
		.data-table tbody tr:hover {
			background-color: rgba(0,0,0,0.02);
			transition: background-color 0.2s ease;
		}

		/* Improve number formatting */
		.data-table td:not(:first-child) {
			font-family: 'Consolas', monospace;
			font-size: 0.9rem;
		}

		/* Add box shadow to table container */
		.data-table-container {
			box-shadow: 0 2px 4px rgba(0,0,0,0.05);
			border-radius: 4px;
			background: white;
		}

		@keyframes spin {
			0% {
				transform: rotate(0deg);
			}
			100% {
				transform: rotate(360deg);
			}
		}

		.btn-group .btn-outline-primary {
			border-color: #3498db;
			color: #3498db;
		}

		.btn-group .btn-outline-primary.active {
			background-color: #3498db;
			color: white;
		}

		.btn-group .btn-outline-primary:hover {
			background-color: #2980b9;
			border-color: #2980b9;
			color: white;
		}

		.chart-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 10px;
		}

		.download-btn {
			background: none;
			border: none;
			color: #6c757d;
			cursor: pointer;
			padding: 5px;
			transition: color 0.2s;
		}

		.download-btn:hover {
			color: #0d6efd;
		}
	</style>
</head>
<body>
	<!-- Navigation Bar -->
	<nav class="navbar navbar-expand-lg navbar-dark bg-dark">
		<div class="container-fluid">
			<a class="navbar-brand" href="/">
				<i class="fas fa-chart-line me-2"></i>WDA Monitor
			</a>
			<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
				<span class="navbar-toggler-icon"></span>
			</button>
			<div class="collapse navbar-collapse" id="navbarNav">
				<ul class="navbar-nav me-auto">
					<li class="nav-item">
						<a class="nav-link" href="/">Home</a>
					</li>
					<li class="nav-item">
						<a class="nav-link active" href="/visuals">Visualizations</a>
					</li>
					<li class="nav-item">
						<a class="nav-link" href="/import-status">Import Status</a>
					</li>
				</ul>
				<ul class="navbar-nav">
					<li class="navbar-text me-3">
						<i class="fas fa-user me-1"></i>
						<span id="userFullName">{{ user.full_name if user else 'User' }}</span>
						<span class="badge bg-light text-dark ms-1" id="userRole">{{ user.role if user else 'Unknown' }}</span>
					</li>
					{% if user and user.role == 'admin' %}
					<li class="nav-item">
						<a class="nav-link" href="/admin/dashboard">
							<i class="fas fa-cog me-1"></i>Admin
						</a>
					</li>
					{% endif %}
					<li class="nav-item">
						<a class="nav-link" href="#" onclick="logout()">
							<i class="fas fa-sign-out-alt me-1"></i>Logout
						</a>
					</li>
				</ul>
			</div>
		</div>
	</nav>

	<!-- Notification Container -->
	<div id="notification" class="notification"></div>

	<!-- Loading Spinner -->
	<!--
	<div id="loading" class="loading" style="display: none;">
		<div class="loading-spinner"></div>
	</div>
	-->
	<!-- Main Content -->
	<div class="container-fluid mt-5">
		<!-- Filter Section -->
		<div class="row filter-section">
			<div class="col-md-2" id="FilterSection">
				<button id="downloadFiltered" class="btn btn-outline-secondary w-100 mb-3">Download Filtered</button>
				<button id="uploadFilteredToAmazon" class="btn btn-outline-secondary w-100 mb-3">
					<i class="fas fa-cloud-upload-alt"></i> Upload Filtered to Amazon
				</button>
				<div class="filter-wrapper mb-3">
					<label class="form-label">Index Range</label>
					<div class="input-group">
						<input type="number" class="form-control" id="startIndex" placeholder="First Index" min="0">
						<input type="number" class="form-control" id="endIndex" placeholder="Last Index" min="0">
						<button class="btn btn-outline-secondary" id="clearIndexFilter">
							<i class="fas fa-times"></i>
						</button>
					</div>
				</div>

				<button id="clearFilters" class="btn btn-outline-secondary w-100 mb-3">
					<i class="fas fa-filter"></i> Clear All Filters
				</button>
				<button id="refreshData" class="btn btn-outline-secondary w-100 mb-3">
					<i class="fas fa-sync-alt"></i> Refresh Data
				</button>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Module</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="moduleFilter">Select All</button>
					</div>
					<select class="form-select select2" id="moduleFilter" multiple>
					</select>
					<span class="filter-count" id="moduleCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">File Name</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="file_nameFilter">Select All</button>
					</div>
					<select class="form-select select2" id="file_nameFilter" multiple>
					</select>
					<span class="filter-count" id="fileNameCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Manufacturer</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="manFilter">Select All</button>
					</div>
					<select class="form-select select2" id="manFilter" multiple>
					</select>
					<span class="filter-count" id="manCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Status</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="statusFilter">Select All</button>
					</div>
					<select class="form-select select2" id="statusFilter" multiple>
					</select>
					<span class="filter-count" id="statusCount"></span>
				</div>
				<!--<div class="filter-wrapper mb-3">
					<label class="form-label">Status</label>
					<select class="form-select select2" id="statusFilter" multiple>
						<option value="found">Success</option>
						<option value="Error">Error (Errors/Exceptions/Incomplete)</option>
						<option value="Proxy">Proxy (403 Errors)</option>
						<option value="not run">Not Run</option>
					</select>
					<span class="filter-count" id="statusCount"></span>
				</div>-->

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Priority</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="prtyFilter">Select All</button>
					</div>
					<select class="form-select select2" id="prtyFilter" multiple>
					</select>
					<span class="filter-count" id="prtyCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Expiration</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="is_expiredFilter">Select All</button>
					</div>
					<select class="form-select select2" id="is_expiredFilter" multiple>
						<option value="true">Expired</option>
						<option value="false">Not Expired</option>
					</select>
					<span class="filter-count" id="expiredCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Table Name</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="table_nameFilter">Select All</button>
					</div>
					<select class="form-select select2" id="table_nameFilter" multiple>
					</select>
					<span class="filter-count" id="table_nameCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Issue Modules</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="issue_modulesFilter">Select All</button>
					</div>
					<select class="form-select select2" id="issue_modulesFilter" multiple>
						<option value="true">Has Issues</option>
						<option value="false">No Issues</option>
					</select>
					<span class="filter-count" id="issue_modulesCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Done Status</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="doneFilter">Select All</button>
					</div>
					<select class="form-select select2" id="doneFilter" multiple>
						<option value="1">Done</option>
						<option value="0">Not Done</option>
					</select>
					<span class="filter-count" id="doneCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Running Status</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="running_statusFilter">Select All</button>
					</div>
					<select class="form-select select2" id="running_statusFilter" multiple>
						<option value="Stopped">Stopped</option>
						<option value="Regular Running">Regular Running</option>
						<option value="Run By Request">Run By Request</option>
						<option value="schedule Running">Schedule Running</option>
					</select>
					<span class="filter-count" id="running_statusCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<div class="d-flex justify-content-between align-items-center mb-2">
						<label class="form-label mb-0">Direct Feed</label>
						<button type="button" class="btn btn-sm btn-outline-secondary select-all-btn" data-target="direct_feedFilter">Select All</button>
					</div>
					<select class="form-select select2" id="direct_feedFilter" multiple>
						<option value="1">Yes</option>
						<option value="0">No</option>
					</select>
					<span class="filter-count" id="direct_feedCount"></span>
				</div>

				<div class="filter-wrapper mb-3">
					<label class="form-label">Date Range</label>
					<div class="input-group">
						<input type="date" class="form-control" id="startDate">
						<input type="date" class="form-control" id="endDate">
						<button class="btn btn-outline-secondary" id="clearDateFilter">
							<i class="fas fa-times"></i>
						</button>
					</div>
				</div>


			</div>

			<!-- Right Column - Visualizations -->
			<div class="col-md-10">

		<!-- Stats Cards -->
		<div class="row mb-4">
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card total-parts">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Total Parts</h6>
								<h2 class="card-title mb-0" id="totalPartsCount">0</h2>
							</div>
							<i class="fas fa-boxes stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card expired-parts">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Expired Parts</h6>
								<h2 class="card-title mb-0" id="expiredPartsCount">0</h2>
							</div>
							<i class="fas fa-exclamation-circle stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card active-parts">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Active Parts</h6>
								<h2 class="card-title mb-0" id="activePartsCount">0</h2>
							</div>
							<i class="fas fa-check-circle stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card error-parts">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Error Parts</h6>
								<h2 class="card-title mb-0" id="errorPartsCount">0</h2>
							</div>
							<i class="fas fa-times-circle stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card missing-parts">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Missing Parts</h6>
								<h2 class="card-title mb-0" id="missingPartsCount">0</h2>
							</div>
							<i class="fas fa-question-circle stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card module-count">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Total Modules</h6>
								<h2 class="card-title mb-0" id="modulesCount">0</h2>
							</div>
							<i class="fas fa-cube stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Validation Stats Section -->
		<h4 class="mb-3">Validation Statistics</h4>
		<div class="row mb-4">
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card" style="background: linear-gradient(45deg, #FF6B6B, #FF8E8E);">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Stopped Modules</h6>
								<h2 class="card-title mb-0" id="stoppedModulesCount">0</h2>
								<small class="text-white" id="stoppedModulesPercentage">0%</small>
							</div>
							<i class="fas fa-stop-circle stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card" style="background: linear-gradient(45deg, #FF6B6B, #FF8E8E);">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Stopped Parts</h6>
								<h2 class="card-title mb-0" id="stoppedPartsCount">0</h2>
								<small class="text-white" id="stoppedPartsPercentage">0%</small>
							</div>
							<i class="fas fa-exclamation-triangle stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card" style="background: linear-gradient(45deg, #FFA500, #FFB74D);">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Pending Modules</h6>
								<h2 class="card-title mb-0" id="pendingModulesCount">0</h2>
								<small class="text-white" id="pendingModulesPercentage">0%</small>
							</div>
							<i class="fas fa-clock stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card" style="background: linear-gradient(45deg, #FFA500, #FFB74D);">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Pending Parts</h6>
								<h2 class="card-title mb-0" id="pendingPartsCount">0</h2>
								<small class="text-white" id="pendingPartsPercentage">0%</small>
							</div>
							<i class="fas fa-hourglass-half stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card" style="background: linear-gradient(45deg, #4CAF50, #81C784);">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">Direct Feed</h6>
								<h2 class="card-title mb-0" id="direct_feedCount">0</h2>
								<small class="text-white" id="direct_feedPercentage">0%</small>
							</div>
							<i class="fas fa-database stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
			<div class="col-md-4 col-lg-2">
				<div class="card stat-card" id="fileStatusCard">
					<div class="card-body">
						<div class="d-flex justify-content-between align-items-center">
							<div>
								<h6 class="card-subtitle mb-2">File Status</h6>
								<h2 class="card-title mb-0" id="fileStatus">-</h2>
								<small class="text-white" id="criticalPercentage">0%</small>
							</div>
							<i class="fas fa-file-alt stat-icon"></i>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Charts Section -->
		<div class="row">
			<div class="row mt-4">
				<div class="col-12">
					<div class="data-table-wrapper">
						<div class="chart-header">
							<h4>File Statistics</h4>
							<button class="download-btn" onclick="downloadChartData('fileStats')">
								<i class="fas fa-download"></i>
							</button>
						</div>
						<div class="table-legend">
							<div class="legend-item">
								<div class="legend-color good"></div>
								<span>Good (≥75% for success metrics, <25% for error metrics)</span>
							</div>
							<div class="legend-item">
								<div class="legend-color warning"></div>
								<span>Warning (50-75% for success, 25-50% for error)</span>
							</div>
							<div class="legend-item">
								<div class="legend-color critical"></div>
								<span>Critical (<50% for success, >50% for error)</span>
							</div>
						</div>
						<div class="data-table-container">
							<table class="data-table" id="fileTable">
								<thead>
									<tr>
										<th data-sort="file">File Name <span class="sort-icon">↕</span></th>
										<th data-sort="total_count">Total Count <span class="sort-icon">↕</span></th>
										<th data-sort="error_count">Error Count <span class="sort-icon">↕</span></th>
										<th data-sort="error_percentage">Error % (Lower Better) <span class="sort-icon">↕</span></th>
										<th data-sort="found_count">Found Count <span class="sort-icon">↕</span></th>
										<th data-sort="found_percentage">Found % (Higher Better) <span class="sort-icon">↕</span></th>
										<th data-sort="done_percentage">Done % (Higher Better) <span class="sort-icon">↕</span></th>
									</tr>
								</thead>
								<tbody id="fileTableBody">
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
			<!-- Status Distribution Chart -->
			<div class="col-md-4 chart-container">
				<div class="chart-header">
					<h4>Status Distribution</h4>
					<button class="download-btn" onclick="downloadChartData('status')">
						<i class="fas fa-download"></i>
					</button>
				</div>
				<div class="position-relative h-100">
					<div id="statusChart" class="chart-container"></div>
					<div class="chart-loading" style="display: none;">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Is Expired Chart -->
			<div class="col-md-4 chart-container">
				<div class="chart-header">
					<h4>Expired vs Valid</h4>
					<button class="download-btn" onclick="downloadChartData('expired')">
						<i class="fas fa-download"></i>
					</button>
				</div>
				<div class="position-relative h-100">
					<div id="isExpiredChart" class="chart-container"></div>
					<div class="chart-loading" style="display: none;">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
			</div>

			<!-- Table Name Distribution Chart -->
			<div class="col-md-4 chart-container">
				<div class="chart-header">
					<h4>Table Name Distribution</h4>
					<button class="download-btn" onclick="downloadChartData('tableName')">
						<i class="fas fa-download"></i>
					</button>
				</div>
				<div class="position-relative h-100">
					<div id="tableNameChart" class="chart-container"></div>
					<div class="chart-loading" style="display: none;">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
			</div>
		</div>


		<!-- Module Distribution and Timeline Charts Row -->
		<div class="row mt-4">
			<!-- Module Distribution Chart -->
			<div class="col-md-6 chart-container">
				<div class="chart-header">
					<h4>Module Distribution</h4>
					<button class="download-btn" onclick="downloadChartData('module')">
						<i class="fas fa-download"></i>
					</button>
				</div>
				<div class="module-chart-wrapper">
					<div class="position-relative h-100">
						<div id="moduleChart"></div>
						<div class="chart-loading" style="display: none;">
							<div class="spinner-border text-primary" role="status">
								<span class="visually-hidden">Loading...</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- Timeline Chart -->
			<div class="col-md-6 chart-container">
				<div class="chart-header">
					<h5>Timeline</h5>
					<button class="download-btn" onclick="downloadChartData('timeline')">
						<i class="fas fa-download"></i>
					</button>
				</div>
				<div class="btn-group mb-3" role="group" aria-label="Time Granularity">
					<button type="button" class="btn btn-outline-primary active" data-granularity="day">Day</button>
					<button type="button" class="btn btn-outline-primary" data-granularity="month">Month</button>
					<button type="button" class="btn btn-outline-primary" data-granularity="quarter">Quarter</button>
					<button type="button" class="btn btn-outline-primary" data-granularity="year">Year</button>
				</div>
				<div class="position-relative h-100">
					<div id="timelineChart" class="chart-container"></div>
					<div class="chart-loading" style="display: none;">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Loading...</span>
						</div>
					</div>
				</div>
				<!-- Hidden Tooltip for Pie Chart -->
				<div id="tooltip" style="
					position: absolute;
					display: none;
					background: white;
					border: 1px solid #ccc;
					padding: 10px;
					border-radius: 8px;
					box-shadow: 2px 2px 10px rgba(0,0,0,0.2);
					z-index: 1000;
				">
					<div id="distributionChart" style="width: 250px; height: 250px;"></div>
				</div>


			</div>
		</div>
		<!-- Data Table Section -->
		<div class="row mt-4">
			<div class="col-12">
				<div class="data-table-wrapper">
					<div class="chart-header">
						<h4 class="chart-title">Module Statistics</h4>
						<button class="download-btn" onclick="downloadChartData('moduleStats')">
							<i class="fas fa-download"></i>
						</button>
					</div>
					<div class="table-legend">
						<div class="legend-item">
							<div class="legend-color good"></div>
							<span>Good (≥75% for success metrics, <25% for error metrics)</span>
						</div>
						<div class="legend-item">
							<div class="legend-color warning"></div>
							<span>Warning (50-75% for success, 25-50% for error)</span>
						</div>
						<div class="legend-item">
							<div class="legend-color critical"></div>
							<span>Critical (<50% for success, >50% for error)</span>
						</div>
					</div>
					<div class="data-table-container">
						<table class="data-table" id="dataTable">
							<thead>
								<tr>
									<th data-sort="module">Module <span class="sort-icon">↕</span></th>
									<th data-sort="running_status">Running Status <span class="sort-icon">↕</span></th>
									<th data-sort="matrix_status">Matrix Status <span class="sort-icon">↕</span></th>
									<th data-sort="matrix_comment">Matrix comment <span class="sort-icon">↕</span></th>
									<th data-sort="matrix_old">Matrix old <span class="sort-icon">↕</span></th>
									<th data-sort="direct_feed_status">direct_feed <span class="sort-icon">↕</span></th>
									<th data-sort="total_count">Total Count <span class="sort-icon">↕</span></th>
									<th data-sort="error_count">Error Count <span class="sort-icon">↕</span></th>
									<th data-sort="error_percentage">Error % (Lower Better) <span class="sort-icon">↕</span></th>
									<th data-sort="found_count">Found Count <span class="sort-icon">↕</span></th>
									<th data-sort="found_percentage">Found % (Higher Better) <span class="sort-icon">↕</span></th>
									<th data-sort="expired_count">Expired Count <span class="sort-icon">↕</span></th>
									<th data-sort="expired_percentage">Expired % (Lower Better) <span class="sort-icon">↕</span></th>
									<th data-sort="recent_count">Recent Count <span class="sort-icon">↕</span></th>
									<th data-sort="recent_percentage">Recent % (Higher Better) <span class="sort-icon">↕</span></th>
								</tr>
							</thead>
							<tbody id="dataTableBody">
							</tbody>
						</table>
					</div>
				</div>
			</div>


		</div>
		<!-- File Statistics Table -->
		<iframe title="Crawler_Trends" width="1140" height="541.25" src="https://app.powerbi.com/reportEmbed?reportId=1707dd29-bef5-48e0-a05a-c42dab3bd84d&autoAuth=true&ctid=0beb0c35-9cbb-4feb-99e5-589e415c7944" frameborder="0" allowFullScreen="false"></iframe>
	</div>
		</div>
	</div>
	<script>

		const commonLayout = {
			height: 400,
			margin: { t: 50, b: 50, l: 50, r: 50 },
			paper_bgcolor: 'white',
			plot_bgcolor: 'white',
			font: { family: 'Arial', size: 12, color: '#506784' },
			showlegend: true
		};
		const commonConfig = {
			responsive: true,
			displayModeBar: true,
			displaylogo: false,
			modeBarButtonsToRemove: ['lasso2d', 'select2d'],
			toImageButtonOptions: {
				format: 'png',
				filename: 'chart',
				height: 500,
				width: 700,
				scale: 2
			}
		};

		let currentGranularity = 'day';
		let lastHoveredPoint = null;
		let hoverTimeout = null;
		let maxDistance = 250; // Max pixels away before hiding chart

		function updateTimelineChart(data) {
			if (!data.timeline) return;

			const timelineData = processTimelineData(data.timeline, currentGranularity);

			const trace = {
				x: timelineData.dates,
				y: timelineData.counts,
				type: 'scatter',
				mode: 'lines+markers',
				line: { color: '#2ecc71', width: 2 },
				marker: { color: '#27ae60', size: 8 }
			};

			const layout = {
				...commonLayout,
				title: { text: `Timeline (by ${currentGranularity})`, font: { size: 16 } },
				xaxis: {
					title: 'Date',
					tickangle: -45,
					automargin: true
				},
				yaxis: {
					title: 'Count',
					rangemode: 'tozero'
				},
				showlegend: false
			};
			const chartDiv = document.getElementById('timelineChart');
			const tooltip = document.getElementById('tooltip'); // Tooltip div

			Plotly.newPlot(chartDiv, [trace], layout, commonConfig).then(function() {

				chartDiv.on('plotly_hover', function(eventData) {
					if (!eventData.points.length) return;

					let hoveredPoint = eventData.points[0]; // The point being hovered
					let hoveredDate = hoveredPoint.x; // Extract date

					let chartContainer = chartDiv.getBoundingClientRect(); // Get chart's position on the page
					// Convert data coordinates to screen coordinates
					let xPixel =  100 +hoveredPoint.xaxis.l2p(new Date(hoveredPoint.x).getTime());
					let yPixel = chartContainer.top + hoveredPoint.yaxis.l2p(hoveredPoint.y);
					console.log(currentGranularity)
					// Fetch data from Flask API
					fetch(`/get_status_by_date?date=${hoveredDate}&grant=${currentGranularity}`)
						.then(response => response.json())
						.then(data => {
							console.log(data.status_counts)
							if (data.status_counts) {
								let labels = Object.keys(data.status_counts);
								let values = Object.values(data.status_counts);

								// Define Pie Chart Data
								let pieData = [{
									labels: labels,
									values: values,
									type: 'pie',
									hole: 0.4, // Donut style
									marker: { colors: ['#2ecc71', '#e74c3c', '#f1c40f', '#3498db'] },
									textinfo: 'label+percent', // Show labels and percentage on sectors
    								insidetextorientation: 'radial' // Align text inside the sectors
								}];

								let pieLayout = {
									title: `Status for ${hoveredDate}`,
									height: 250,
									width: 250,
									margin: { t: 30, l: 10, r: 10, b: 10 },
									showlegend: false
								};

								// Show and position tooltip
								tooltip.style.display = "block";
								tooltip.style.left = `${xPixel }px`;
								tooltip.style.top = `${yPixel}px`;

								// Update the floating pie chart
								Plotly.react('distributionChart', pieData, pieLayout);
							}
						})
						.catch(error => console.error("Error fetching data:", error));
				});

				// Unhover event to hide chart
				chartDiv.on('plotly_unhover', function() {
					hoverTimeout = setTimeout(() => {
						if (!lastHoveredPoint) return;

						let onMouseMove = (event) => {
							let distX = Math.abs(event.clientX - (chartDiv.getBoundingClientRect().left + lastHoveredPoint.x));
							let distY = Math.abs(event.clientY - (chartDiv.getBoundingClientRect().top + lastHoveredPoint.y));

							// Hide chart if mouse moves too far
							if (distX > maxDistance || distY > maxDistance) {
								tooltip.style.display = "none";
								document.removeEventListener('mousemove', onMouseMove);
								lastHoveredPoint = null;
							}
						};

						document.addEventListener('mousemove', onMouseMove);
					}, 500); // Delay to prevent flickering
				});

				// Hide chart when leaving the chart area
				chartDiv.addEventListener('mouseleave', () => {
					tooltip.style.display = "none";
					lastHoveredPoint = null;
					try{
						document.removeEventListener('mousemove', onMouseMove);
					}catch{}
				});
			});
		}

		function processTimelineData(timeline, granularity) {
			const dateMap = new Map();

			timeline.dates.forEach((date, index) => {
				let key = date;
				if (granularity === 'month') {
					key = date.substring(0, 7); // YYYY-MM
				} else if (granularity === 'quarter') {
					const month = parseInt(date.substring(5, 7));
					const quarter = Math.ceil(month / 3);
					key = `${date.substring(0, 4)}-Q${quarter}`;
				} else if (granularity === 'year') {
					key = date.substring(0, 4); // YYYY
				}

				dateMap.set(key, (dateMap.get(key) || 0) + timeline.counts[index]);
			});

			const sortedDates = Array.from(dateMap.keys()).sort();
			return {
				dates: sortedDates,
				counts: sortedDates.map(date => dateMap.get(date))
			};
		}

		let currentFilters = {
			module: [],
			file_name: [],
			man: [],
			status: [],
			prty: [],
			is_expired: [],
			table_name: [],
			issue_modules: [],
			done: [],
			running_status: [],  // Added for Running Status filter
			direct_feed: [],     // Added for Direct Feed filter
			startDate: '',
			endDate: '',
			startIndex: null,
			endIndex: null
		};
		// Data Table functionality
		let tableData = [];
		let currentSortColumn = '';
		let sortDirection = 'asc';

		// Cache implementation
		const filterCache = new Map();
		const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes in milliseconds



		function getCacheKey(filters) {
			return JSON.stringify(filters);
		}

		function getCachedData(filters) {
			const key = getCacheKey(filters);
			const cached = filterCache.get(key);
			if (cached && Date.now() - cached.timestamp < CACHE_EXPIRY) {
				return cached.data;
			}
			return null;
		}
		function setCachedData(filters, data) {
			const key = getCacheKey(filters);
			filterCache.set(key, {
				data: data,
				timestamp: Date.now()
			});
		}

		function updateFilterCounts() {
				Object.keys(currentFilters).forEach(key => {
					if (key !== 'startDate' && key !== 'endDate' && key !== 'startIndex' && key !== 'endIndex' ) {
						const count = currentFilters[key].length;
						const countElement = $(`#${key}Count`);
						if (countElement.length) {
							if (count > 0) {
								countElement.text(`(${count})`).show()
									.css({
										'display': 'inline-block',
										'margin-left': '5px',
										'background-color': '#e9ecef',
										'padding': '2px 6px',
										'border-radius': '10px',
										'font-size': '0.8em'
									});
							} else {
								countElement.hide();
							}
						}
					}
				});
			}


		// Update loadFilterOptions to preserve selected values
		async function loadFilterOptions() {
			try {
				const response = await fetch('/api/filter-options');
				const data = await response.json();

				// Store current selections
				const currentSelections = {
					module: $('#moduleFilter').val(),
					file_name: $('#file_nameFilter').val(),
					man: $('#manFilter').val(),
					status: $('#statusFilter').val(),
					prty: $('#prtyFilter').val(),
					table_name: $('#table_nameFilter').val(),
					is_expired: $('#is_expiredFilter').val(),
					issue_modules: $('#issue_modulesFilter').val(),
					done: $('#doneFilter').val(),
					running_status: $('#running_statusFilter').val(),
					direct_feed: $('#direct_feedFilter').val()
				};

				// Initialize select2 dropdowns while preserving selections
				const initSelect2 = (elementId, options, placeholder) => {
					const currentVal = $(`#${elementId}`).val();
					$(`#${elementId}`).empty().select2({

						data: options.map(opt => ({ id: opt, text: opt })),
						allowClear: true,

						placeholder: placeholder,
						minimumResultsForSearch: Infinity,  // hides search box
  						//templateSelection: null,        // hides selected item
						multiple: true,
						width: '100%'
					});
					if (currentVal) {
						$(`#${elementId}`).val(currentVal).trigger('change.select2');
					}
				};
				console.log(data.teams)
				initSelect2('moduleFilter', data.teams, 'Select modules');
				initSelect2('file_nameFilter', data.Files, 'Select files');
				initSelect2('manFilter', data.manufacturers, 'Select manufacturers');
				initSelect2('statusFilter', data.status, 'Select Status');
				initSelect2('prtyFilter', data.priorities, 'Select priorities');
				initSelect2('table_nameFilter', data.table_name, 'Select table name');
				initSelect2('is_expiredFilter', ['true', 'false'], 'Select expiration status');
				initSelect2('issue_modulesFilter', ['true', 'false'], 'Select issue status');
				initSelect2('doneFilter', ['1', '0'], 'Select done status');
				initSelect2('running_statusFilter', data.running_status || ['Stopped', 'Regular Running', 'Run By Request', 'schedule Running'], 'Select running status');
				initSelect2('direct_feedFilter', ['1', '0'], 'Select direct feed status');

				// Restore all selections and update filter counts
				Object.entries(currentSelections).forEach(([key, value]) => {
					if (value && value.length) {
						$(`#${key}Filter`).val(value).trigger('change.select2');
						currentFilters[key] = value;
					}
				});

				// Update filter counts after restoring selections
				updateFilterCounts();

			} catch (error) {
				console.error('Error loading filter options:', error);
				showNotification('Error loading filter options', 'error');
			}
		}



		// Add error boundary for chart updates
		function handleChartError(chartId, error) {
			console.error(`Error updating ${chartId}:`, error);
			$(`#${chartId}`).html(`
				<div class="chart-error">
					<div>
						<i class="fas fa-exclamation-circle fa-2x mb-2"></i>
						<p>Failed to load chart: ${error.message}</p>
						<button class="btn btn-sm btn-outline-danger mt-2" onclick="retryChart('${chartId}')">
							<i class="fas fa-sync-alt"></i> Retry
						</button>
					</div>
				</div>
			`);
		}

		// Add retry function
		async function retryChart(chartId) {
			$(`#${chartId}`).empty();
			$(`#${chartId}`).closest('.chart-container').find('.chart-loading').css('display', 'flex');
			try {
				await updateCharts();
			} catch (error) {
				handleChartError(chartId, error);
			}
		}

		async function updateSingleChart(chartId, updateFn) {
			try {
				await updateFn();
			} catch (error) {
				handleChartError(chartId, error);
			}
		}

		async function updateCharts() {
			// Show loading indicators
			$('.chart-loading, #loading').css('display', 'flex');

			try {
				let handled_data = {}
				// Check cache first
				const cachedData = getCachedData(currentFilters);

				if (cachedData) {
					console.log('Using cached data:', cachedData); // Debug log
					handled_data = cachedData;
				} else {
					// Fetch new data
					const response = await fetch('/api/chart-data', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify(currentFilters)
					});

					if (!response.ok) {
						throw new Error('Network response was not ok');
					}

					handled_data = await response.json();
					console.log('Fetched new data:', handled_data); // Debug log
					setCachedData(currentFilters, handled_data);
				}

				// Update validation cards first
				updateValidationCards(handled_data);

				// Update other charts...
				// (your existing chart update code)

				const data = handled_data

				// Update stats
				$('#totalPartsCount').text(data.stats.totalParts || 0);
				$('#expiredPartsCount').text(data.stats.expiredParts || 0);
				$('#activePartsCount').text(data.stats.activeParts || 0);
				$('#errorPartsCount').text(data.stats.errorParts || 0);
				$('#missingPartsCount').text(data.stats.missingParts || 0);
				$('#modulesCount').text(data.stats.moduleCount || 0);

				const commonConfig = {
					responsive: true,
					displayModeBar: true,
					displaylogo: false,
					modeBarButtonsToRemove: ['lasso2d', 'select2d'],
					toImageButtonOptions: {
						format: 'png',
						filename: 'chart',
						height: 500,
						width: 700,
						scale: 2
					}
				};



				// Update charts with individual error handling
				const updatePromises = [
					updateSingleChart('statusChart', () => Plotly.newPlot('statusChart', [{
						values: data.status.values,
						labels: data.status.labels,
						type: 'pie',
						hole: 0.4,
						textinfo: 'label+percent',
						hoverinfo: 'label+value+percent',
						marker: {
							colors: [
								'#2ecc71',  // Success - Green
								'#e74c3c',  // Error - Red
								'#f39c12',  // Proxy - Orange
								'#95a5a6'   // Not Run - Gray
							]
						}
					}], {
						...commonLayout,
						title: { text: 'Status Distribution', font: { size: 16 } }
					}, commonConfig)),

					updateSingleChart('isExpiredChart', () => Plotly.newPlot('isExpiredChart', [{
						values: data.isExpired.values,
						labels: data.isExpired.labels,
						type: 'pie',
						hole: 0.4,
						textinfo: 'label+percent',
						hoverinfo: 'label+value+percent',
						marker: { colors: ['#e74c3c', '#2ecc71'] }
					}], {
						...commonLayout,
						title: { text: 'Expiration Status', font: { size: 16 } }
					}, commonConfig)),

					updateSingleChart('tableNameChart', () => Plotly.newPlot('tableNameChart', [{
						y: data.table_name.labels,
						x: data.table_name.values,
						type: 'bar',
						orientation: 'h',
						marker: { color: '#9b59b6' }
					}], {
						...commonLayout,
						title: { text: 'Table Name Distribution', font: { size: 16 } },
						margin: { t: 50, b: 50, l: 150, r: 50 },
						xaxis: { title: 'Count' },
						yaxis: { title: '', automargin: true },
						showlegend: false
					}, commonConfig)),

					updateSingleChart('moduleChart', () => {
						// Sort and get top 10 modules
						const sortedData = data.team.labels.map((label, i) => ({
							label: label,
							value: data.team.values[i]
						})).sort((a, b) => b.value - a.value);

						// Create the chart data
						const chartData = [{
							x: sortedData.map(d => d.label.slice(0,29)),
							y: sortedData.map(d => d.value),
							type: 'bar',
							marker: {
								color: '#3498db',
								width: 0.6  // Make bars slightly thinner
							},
							name: 'Top 10 Modules',
							hovertemplate: '<b>%{x}</b><br>Count: %{y}<extra></extra>'
						}];

						updateDataTable(data);

						updateFileTable(data);

						// Create layout with fixed dimensions
						/*const moduleLayout = {
							...commonLayout,
							title: {
								text: 'Module Distribution (Top 10)',
								font: { size: 16 },
								pad: { t: 20 }
							},
							margin: { t: 60, b: 120, l: 80, r: 20 },
							autosize: false,
							width: 1100,
							height: 500,
							xaxis: {
								tickangle: -45,
								tickfont: { size: 11 },
								range: [-0.5, 9.5],
								title: {
									text: 'Module Name',
									standoff: 40
								},
								fixedrange: false,
								constrain: 'domain',
								automargin: true,
								showgrid: false  // Remove grid lines
							},
							yaxis: {
								title: 'Count',
								fixedrange: true,
								automargin: true,
								rangemode: 'tozero',
								showgrid: true,  // Keep grid lines for y-axis
								gridcolor: '#E1E1E1'  // Light gray grid lines
							},
							showlegend: false,
							dragmode: 'pan',
							plot_bgcolor: 'white',
							paper_bgcolor: 'white',
							bargap: 0.15,
							uniformtext: {
								mode: 'hide',
								minsize: 8
							}
						};
						*/
						const moduleLayout = {
    ...commonLayout,
    title: {
        text: 'Module Distribution (Top 10)',
        font: { size: 16 },
        pad: { t: 20 }
    },
    margin: { t: 60, b: 120, l: 80, r: 20 },
    autosize: false,
    //width: 1100,
    height: 500,
    xaxis: {
        tickangle: -25,
        tickfont: { size: 11 },
        range: [-0.5, 9.5],
        title: {
            text: 'Module Name',
            standoff: 40
        },
        fixedrange: false,
        constrain: 'domain',
        automargin: true,
        showgrid: false,
        ticktext: sortedData.slice(0, 10).map(d => d.label.length > 20 ? d.label.substring(0, 20) + '...' : d.label),
        tickvals: sortedData.slice(0, 10).map((_, i) => i)
    },
    yaxis: {
        title: 'Count',
        fixedrange: true,
        automargin: true,
        rangemode: 'tozero',
        showgrid: true,
        gridcolor: '#E1E1E1'
    },
    showlegend: false,
    dragmode: 'pan',
    plot_bgcolor: 'white',
    paper_bgcolor: 'white',
    bargap: 0.15,
    uniformtext: {
        mode: 'hide',
        minsize: 8
    }
};


						// Update config
						const moduleConfig = {
							...commonConfig,
							responsive: false,
							scrollZoom: true,
							modeBarButtonsToAdd: [{
								name: 'Show All',
								icon: Plotly.Icons.home,
								click: function(gd) {
									Plotly.update(gd, {
										x: [sortedData.map(d => d.label)],
										y: [sortedData.map(d => d.value)]
									}, {
										'xaxis.range': [-0.5, sortedData.length - 0.5],
										'xaxis.title.text': 'Module Name (scroll/drag to see more)',
										'bargap': 0.1
									});
								}
							}, {
								name: 'Show Top 10',
								icon: Plotly.Icons.autoscale,
								click: function(gd) {
									Plotly.update(gd, {
										x: [sortedData.slice(0, 10).map(d => d.label)],
										y: [sortedData.slice(0, 10).map(d => d.value)]
									}, {
										'xaxis.range': [-0.5, 9.5],
										'xaxis.title.text': 'Module Name',
										'bargap': 0.15
									});
								}
							}],
							displayModeBar: true,
							displaylogo: false,
							modeBarButtonsToRemove: ['select2d', 'lasso2d', 'autoScale2d']
						};

						return Plotly.newPlot('moduleChart', chartData, moduleLayout, moduleConfig);
					})
				];

				if (data.timeline) {
					updatePromises.push(
						updateSingleChart('timelineChart', () => updateTimelineChart(data))
					);
				}



				await Promise.allSettled(updatePromises);
				window.dispatchEvent(new Event('resize'));

			} catch (error) {
				console.error('Error:', error);
				showNotification(error.message || 'An error occurred while updating the charts', 'error');
			} finally {
				setTimeout(() => {
					$('.chart-loading, #loading').fadeOut(200);
				}, 200);
			}
		}

		function updateValidationCards(data) {
			console.log('Updating validation cards with data:', data); // Debug log

			if (!data || !data.validation_stats) {
				console.error('No validation stats found in data');
				return;
			}

			const stats = data.validation_stats;

			try {
				// Update Stopped Modules
				$('#stoppedModulesCount').text(stats.stopped_modules.count || 0);
				$('#stoppedModulesPercentage').text((stats.stopped_modules.percentage || 0) + '%');

				// Update Stopped Parts
				$('#stoppedPartsCount').text(stats.stopped_parts.count || 0);
				$('#stoppedPartsPercentage').text((stats.stopped_parts.percentage || 0) + '%');

				// Update Pending Modules
				$('#pendingModulesCount').text(stats.pending_modules.count || 0);
				$('#pendingModulesPercentage').text((stats.pending_modules.percentage || 0) + '%');

				// Update Pending Parts
				$('#pendingPartsCount').text(stats.pending_parts.count || 0);
				$('#pendingPartsPercentage').text((stats.pending_parts.percentage || 0) + '%');

				// Update Direct Feed
				$('#direct_feedCount').text(stats.direct_feed_modules.count || 0);
				$('#direct_feedPercentage').text((stats.direct_feed_modules.percentage || 0) + '%');

				// Update File Status
				$('#fileStatus').text(stats.file_status || '-');
				$('#criticalPercentage').text((stats.total_critical_percentage || 0) + '%');

				// Update File Status Card color based on status
				const fileStatusCard = document.getElementById('fileStatusCard');
				if (stats.file_status === 'Rejected') {
					fileStatusCard.style.background = 'linear-gradient(45deg, #FF5252, #FF1744)';
				} else {
					fileStatusCard.style.background = 'linear-gradient(45deg, #00C853, #69F0AE)';
				}

				console.log('Validation cards updated successfully');
			} catch (error) {
				console.error('Error updating validation cards:', error);
			}
		}

		// Add cache clear function for the clear filters button
		function clearCache() {
			filterCache.clear();
			console.log('Cache cleared');
		}
		// Notification function
		function showNotification(message, type = 'success') {
			const notification = $('#notification');
			notification.removeClass('success error')
				.addClass(type)
				.text(message)
				.fadeIn()
				.delay(3000)
				.fadeOut();
		}

		// Event handlers
		$(document).ready(function() {
			// Initialize select2 dropdowns with default settings
			$('.select2').select2({
				width: '100%',
				placeholder: 'Select options'
			});

			// Initialize chart loading indicators
			$('.chart-container').each(function() {
				const container = $(this);
				if (!container.find('.chart-loading').length) {
					container.append(`
						<div class="chart-loading" style="display: none;">
							<div class="spinner-border text-primary" role="status">
								<span class="visually-hidden">Loading...</span>
							</div>
						</div>
					`);
				}
			});

			loadFilterOptions();
			updateCharts();

			// Add event handler for the "Select All" buttons
			$(document).on('click', '.select-all-btn', function() {
				const targetId = $(this).data('target');
				const $select = $(`#${targetId}`);
				const $button = $(this);

				// Get all options except "Select All"
				const allOptions = $select.find('option').map(function() {
					return $(this).val();
				}).get().filter(val => val !== 'Select All' && val !== 'Deselect All');

				// Check if all options are already selected
				const currentValues = $select.val() || [];
				const allSelected = allOptions.length === currentValues.filter(val => val !== 'Select All' && val !== 'Deselect All').length;

				if (allSelected) {
					// Deselect all
					$select.val([]).trigger('change');
					$button.text('Select All');
				} else {
					// Select all
					$select.val(allOptions).trigger('change');
					$button.text('Deselect All');
				}
			});

			// Update the select2 change handler to also update the button text
			$('.select2').on('change.updateButton', function() {
				const filterId = this.id;
				const $button = $(`.select-all-btn[data-target="${filterId}"]`);
				const $select = $(this);

				// Get all options except "Select All"
				const allOptions = $select.find('option').map(function() {
					return $(this).val();
				}).get().filter(val => val !== 'Select All' && val !== 'Deselect All');

				// Check if all options are selected
				const currentValues = $select.val() || [];
				const allSelected = allOptions.length > 0 &&
					allOptions.length === currentValues.filter(val => val !== 'Select All' && val !== 'Deselect All').length;

				// Update button text
				$button.text(allSelected ? 'Deselect All' : 'Select All');
			});

			// Add handler for granularity buttons
			$('.btn-group [data-granularity]').click(function() {
				$('.btn-group [data-granularity]').removeClass('active');
				$(this).addClass('active');
				currentGranularity = $(this).data('granularity');

				// Get the cached data or fetch new data
				const cachedData = getCachedData(currentFilters);
				if (cachedData) {
					updateTimelineChart(cachedData);
				} else {
					updateCharts();
				}
			});

			// Update select2 change handler
			$('.select2').on('change', function() {
				const filterId = this.id.replace('Filter', '');
				currentFilters[filterId] = $(this).val() || [];
				updateFilterCounts();
				updateCharts();
			});

			$('#startDate, #endDate').on('change', function() {
				currentFilters.startDate = $('#startDate').val();
				currentFilters.endDate = $('#endDate').val();
				updateCharts();
			});

			$('#clearDateFilter').click(function() {
				$('#startDate, #endDate').val('');
				currentFilters.startDate = '';
				currentFilters.endDate = '';
				updateCharts();
			});

			// Add event listeners for index range inputs
			$('#startIndex, #endIndex').on('change', function() {
				const startIndex = $('#startIndex').val();
				const endIndex = $('#endIndex').val();

				if (startIndex && endIndex && parseInt(startIndex) > parseInt(endIndex)) {
					showNotification('Start index cannot be greater than end index', 'warning');
					$(this).val('');
					return;
				}

				currentFilters.startIndex = startIndex ? parseInt(startIndex) : null;
				currentFilters.endIndex = endIndex ? parseInt(endIndex) : null;
				updateCharts();
			});

			$('#clearIndexFilter').click(function() {
				$('#startIndex, #endIndex').val('');
				currentFilters.startIndex = null;
				currentFilters.endIndex = null;
				updateCharts();
			});

			$('#clearFilters').click(async function() {
				// Clear all select2 dropdowns
				$('.select2').each(function() {
					// Reset the "Select All" button text
					const filterId = this.id;
					$(`.select-all-btn[data-target="${filterId}"]`).text('Select All');

					$(this).val(null).trigger('change');
				});

				// Clear date inputs
				$('#startDate, #endDate').val('');

				// Clear index inputs
				$('#startIndex, #endIndex').val('');

				// Reset current filters object
				currentFilters = {
					module: [],
					file_name: [],
					man: [],
					status: [],
					prty: [],
					is_expired: [],
					table_name: [],
					issue_modules: [],
					done: [],
					running_status: [],  // Added for Running Status filter
					direct_feed: [],     // Added for Direct Feed filter
					startDate: '',
					endDate: '',
					startIndex: null,
					endIndex: null
				};

				// Clear filter counts
				$('.filter-count').hide();

				// Reload filter options to reset dropdowns
				await loadFilterOptions();

				// Update charts with cleared filters
				updateCharts();
			});




			$('#refreshData').click(async function() {
				$('#loading').show();
				try {
					console.log("uploading data")
					// Call update-data API
					const response = await fetch('/update-data');
					if (!response.ok) {
						throw new Error('Failed to update data');
					}

					// Clear the cache
					clearCache();

					// Update charts with fresh data
					await loadFilterOptions();
					await updateCharts();

					showNotification('Data refreshed successfully', 'success');
				} catch (error) {
					console.error('Error refreshing data:', error);
					showNotification('Failed to refresh data: ' + error.message, 'error');
				} finally {
					$('#loading').hide();
				}
			});

			$('#downloadFiltered').click(async function() {
				$('#loading').show();
				try {
					const response = await fetch('/api/download-filtered', {
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify(currentFilters)
					});
					const blob = await response.blob();
					const url = window.URL.createObjectURL(blob);
					const a = document.createElement('a');
					a.href = url;
					a.download = 'filtered_results.csv';
					document.body.appendChild(a);
					a.click();
					window.URL.revokeObjectURL(url);
				} catch (error) {
					console.error('Error downloading filtered results:', error);
				} finally {
					$('#loading').hide();
				}
			});
		});
		function updateDataTable(data) {

			if (!data || !data.tableData) return;

			tableData = data.tableData;
			renderTable();
		}
		function getPercentageClass(percentage, isError = false) {
			if (isError) {
				// For error percentage, higher is worse
				if (percentage >= 50) return 'percentage-high';
				if (percentage >= 25) return 'percentage-medium';
				return 'percentage-low';
			} else {
				// For other percentages (found, recent), higher is better
				if (percentage >= 75) return 'percentage-low';
				if (percentage >= 50) return 'percentage-medium';
				return 'percentage-high';
			}
		}

		function renderTable() {
			const tbody = document.getElementById('dataTableBody');
			tbody.innerHTML = '';
			console.log("aaaaaaaaaaaaaaaasssssssssssssssssssssssssss")
			tableData.forEach(row => {
				const errorClass = getPercentageClass(row.error_percentage || 0, true);
				const foundClass = getPercentageClass(row.found_percentage || 0, false);
				const expiredClass = getPercentageClass(row.expired_percentage || 0, true);
				const recentClass = getPercentageClass(row.recent_percentage || 0, false);

				const tr = document.createElement('tr');
				tr.innerHTML = `
					<td>${row.module || ''}</td>
					<td>${row.running_status || ''}</td>
					<td>${row.matrix_status || ''}</td>
					<td>${row.matrix_comment || ''}</td>
					<td>${row.matrix_old || ''}</td>
					<td>${row.direct_feed_status || ''}</td>
					<td>${row.total_count || 0}</td>
					<td>${row.error_count || 0}</td>
					<td class="${errorClass}" style="--percentage-width: ${row.error_percentage || 0}%">${row.error_percentage || 0}%</td>
					<td>${row.found_count || 0}</td>
					<td class="${foundClass}" style="--percentage-width: ${row.found_percentage || 0}%">${row.found_percentage || 0}%</td>
					<td>${row.expired_count || 0}</td>
					<td class="${expiredClass}" style="--percentage-width: ${row.expired_percentage || 0}%">${row.expired_percentage || 0}%</td>
					<td>${row.recent_count || 0}</td>
					<td class="${recentClass}" style="--percentage-width: ${row.recent_percentage || 0}%">${row.recent_percentage || 0}%</td>
				`;
				tbody.appendChild(tr);
			});
		}

		function sortTable(column) {
			if (currentSortColumn === column) {
				sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
			} else {
				currentSortColumn = column;
				sortDirection = 'asc';
			}

			tableData.sort((a, b) => {
				let valueA = a[column] || 0;
				let valueB = b[column] || 0;

				// Handle numeric columns
				if (column.includes('count') || column.includes('percentage')) {
					valueA = parseFloat(valueA);
					valueB = parseFloat(valueB);
					return sortDirection === 'asc' ? valueA - valueB : valueB - valueA;
				}

				// Handle text columns (module)
				if (sortDirection === 'asc') {
					return valueA.toString().localeCompare(valueB.toString());
				} else {
					return valueB.toString().localeCompare(valueA.toString());
				}
			});

			renderTable();
		}

		// Add event listeners for table sorting
		document.addEventListener('DOMContentLoaded', function() {
			const table = document.getElementById('dataTable');
			if (table) {
				table.querySelector('thead').addEventListener('click', function(e) {
					const th = e.target.closest('th');
					if (th) {
						const column = th.dataset.sort;
						if (column) {
							sortTable(column);
						}
					}
				});
			}
		});



		// File Table functionality
let fileTableData = [];
let currentFileSortColumn = '';
let fileSortDirection = 'asc';

function updateFileTable(data) {
    if (!data || !data.fileStats) return;
    fileTableData = data.fileStats;
    renderFileTable();
}

function renderFileTable() {
    const tbody = document.getElementById('fileTableBody');
    tbody.innerHTML = '';
    fileTableData.forEach(row => {
        const errorClass = getPercentageClass(row.error_percentage || 0, true);
        const foundClass = getPercentageClass(row.found_percentage || 0, false);
        const DoneClass = getPercentageClass(row.done_percentage || 0, false);

        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${row.file || ''}</td>
            <td>${row.total_count || 0}</td>
            <td>${row.error_count || 0}</td>
            <td class="${errorClass}" style="--percentage-width: ${row.error_percentage || 0}%">${row.error_percentage || 0}%</td>
            <td>${row.found_count || 0}</td>
            <td class="${foundClass}" style="--percentage-width: ${row.found_percentage || 0}%">${row.found_percentage || 0}%</td>
            <td class="${DoneClass}" style="--percentage-width: ${row.done_percentage || 0}%">${row.done_percentage || 0}%</td>
        `;
        tbody.appendChild(tr);
    });
}

function sortFileTable(column) {
    if (currentFileSortColumn === column) {
        fileSortDirection = fileSortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        currentFileSortColumn = column;
        fileSortDirection = 'asc';
    }

    fileTableData.sort((a, b) => {
        let valueA = a[column] || 0;
        let valueB = b[column] || 0;

        // Handle numeric columns
        if (column.includes('count') || column.includes('percentage')) {
            valueA = parseFloat(valueA);
            valueB = parseFloat(valueB);
            return fileSortDirection === 'asc' ? valueA - valueB : valueB - valueA;
        }

        // Handle text columns (file)
        if (fileSortDirection === 'asc') {
            return valueA.toString().localeCompare(valueB.toString());
        } else {
            return valueB.toString().localeCompare(valueA.toString());
        }
    });

    renderFileTable();
}

// Add event listeners for file table sorting
document.addEventListener('DOMContentLoaded', function() {
    const fileTable = document.getElementById('fileTable');
    if (fileTable) {
        fileTable.querySelector('thead').addEventListener('click', function(e) {
            const th = e.target.closest('th');
            if (th) {
                const column = th.dataset.sort;
                if (column) {
                    sortFileTable(column);
                }
            }
        });
    }
});

// Update the updateCharts function to include file table update
const existingUpdateCharts = updateCharts;
updateCharts = async function() {
    await existingUpdateCharts();
    const data = getCachedData(currentFilters);
    if (data) {
        updateFileTable(data);
    }
};

function downloadChartData(chartType) {
    const data = getCachedData(currentFilters);
    if (!data) {
        showNotification('No data available to download', 'error');
        return;
    }

    let csvContent = '';
    let filename = '';

    switch(chartType) {
        case 'status':
            csvContent = 'Status,Count\n' +
                data.status.labels.map((label, i) =>
                    `${label},${data.status.values[i]}`).join('\n');
            filename = 'status_distribution.csv';
            break;

        case 'expired':
            csvContent = 'Status,Count\n' +
                data.isExpired.labels.map((label, i) =>
                    `${label},${data.isExpired.values[i]}`).join('\n');
            filename = 'expiration_status.csv';
            break;

        case 'tableName':
            csvContent = 'Table Name,Count\n' +
                data.table_name.labels.map((label, i) =>
                    `${label},${data.table_name.values[i]}`).join('\n');
            filename = 'table_name_distribution.csv';
            break;

        case 'module':
            // Sort data by value in descending order
            const moduleData = data.team.labels.map((label, i) => ({
                label: label,
                value: data.team.values[i]
            })).sort((a, b) => b.value - a.value);

            csvContent = 'Module,Count\n' +
                moduleData.map(item => `${item.label},${item.value}`).join('\n');
            filename = 'module_distribution.csv';
            break;

        case 'timeline':
            if (data.timeline) {
                const processedData = processTimelineData(data.timeline, currentGranularity);
                csvContent = 'Date,Count\n' +
                    processedData.dates.map((date, i) =>
                        `${date},${processedData.counts[i]}`).join('\n');
                filename = 'timeline_data.csv';
            }
            break;

        case 'fileStats':
            // Assuming fileStats data is available in the data object
            const fileHeaders = [
                'File Name',
                'Total Count',
                'Error Count',
                'Error Percentage',
                'Found Count',
                'Found Percentage',
                'Done Percentage'
            ];

            csvContent = fileHeaders.join(',') + '\n';
            data.fileStats.forEach(row => {
                csvContent += `${row.file},${row.total_count},${row.error_count},` +
                    `${row.error_percentage},${row.found_count},${row.found_percentage},` +
                    `${row.done_percentage}\n`;
            });
            filename = 'file_statistics.csv';
            break;

        case 'moduleStats':
            const moduleHeaders = [
                'Module',
                'Running Status',
                'Matrix Status',
                'Matrix Comment',
                'Matrix Old',
                'Total Count',
                'Error Count',
                'Error Percentage',
                'Found Count',
                'Found Percentage',
                'Expired Count'
            ];

            csvContent = moduleHeaders.join(',') + '\n';
            data.moduleStats.forEach(row => {
                csvContent += `${row.module},${row.running_status},${row.matrix_status},` +
                    `${row.matrix_comment},${row.matrix_old},${row.direct_feed_status},${row.total_count},` +
                    `${row.error_count},${row.error_percentage},${row.found_count},` +
                    `${row.found_percentage},${row.expired_count}\n`;
            });
            filename = 'module_statistics.csv';
            break;

        default:
            showNotification('Invalid chart type', 'error');
            return;
    }

    // Handle special characters and quotes in CSV
    csvContent = csvContent.replace(/"/g, '""');
    csvContent = csvContent.replace(/([,\n])(.*?)(,|\n|$)/g, '$1"$2"$3');

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (navigator.msSaveBlob) { // IE 10+
        navigator.msSaveBlob(blob, filename);
    } else {
        link.href = URL.createObjectURL(blob);
        link.setAttribute('download', filename);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Global variable to track Amazon upload status
let isAmazonUploading = false;

$('#uploadFilteredToAmazon').click(async function() {
    if (isAmazonUploading) {
        showNotification('Another upload to Amazon is in progress. Please wait.', 'warning');
        return;
    }

    try {
        isAmazonUploading = true;
        const button = $(this);
        const originalText = button.html();

        // Update button state
        button.prop('disabled', true);
        button.html('<i class="fas fa-sync fa-spin"></i> Uploading...');
        $('#loading').show();

        const response = await fetch('/api/upload-filtered-to-amazon', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(currentFilters)
        });

        const result = await response.json();

        if (response.ok) {
            showNotification('Filtered data uploaded to Amazon successfully', 'success');
        } else {
            throw new Error(result.error || 'Failed to upload filtered data to Amazon');
        }
    } catch (error) {
        console.error('Error uploading filtered data to Amazon:', error);
        showNotification(error.message, 'error');
    } finally {
        isAmazonUploading = false;
        const button = $('#uploadFilteredToAmazon');
        button.prop('disabled', false);
        button.html('<i class="fas fa-cloud-upload-alt"></i> Upload Filtered to Amazon');
        $('#loading').hide();
    }
});

// Authentication functions
async function checkAuth() {
	try {
		const response = await fetch('/check-auth');
		const data = await response.json();

		if (data.authenticated) {
			// Update user info in navbar
			document.getElementById('userFullName').textContent = data.user.full_name;
			document.getElementById('userRole').textContent = data.user.role;

			// Store user info for client-side reference
			localStorage.setItem('userInfo', JSON.stringify(data.user));

			return true;
		} else {
			// Redirect to login page
			window.location.href = '/login';
			return false;
		}
	} catch (error) {
		console.error('Auth check failed:', error);
		window.location.href = '/login';
		return false;
	}
}

async function logout() {
	try {
		const response = await fetch('/logout', { method: 'POST' });
		if (response.ok) {
			localStorage.removeItem('userInfo');
			window.location.href = '/login';
		}
	} catch (error) {
		console.error('Logout failed:', error);
		// Force redirect even if logout request fails
		localStorage.removeItem('userInfo');
		window.location.href = '/login';
	}
}

// Check authentication on page load
document.addEventListener('DOMContentLoaded', async () => {
	await checkAuth();
});

	</script>
</body>
</html>
